"""
Cards card_id endpoint
/api/cards/{card_id}
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock card data
MOCK_CARD = {
    "id": 14,
    "title": "Test card",
    "notes": "Основная карта для тестов",
    "tags": ["test_tags", "test2"],
    "number": "****************",
    "exp_date": "03/25",
    "cvv": "123",
    "ordered_time": 1723423423,
    "issued_time": 1723423423,
    "last_transaction": 1745645645,
    "card_type": "balanced",
    "balance_id": -1,
    "balance": 500,
    "limit": 500,
    "scheme": "visa",
    "can_topup": True,
    "spend": 123.42,
    "hold": 0.00,
    "decline_rate": 0.00,
    "daily_spend": 50.00,
    "total_spend": 123.42,
    "daily_hold": 10.00,
    "total_hold": 0.00,
    "status": "active"
}

@GET
def get(request):
    """Get card information by ID"""
    try:
        # Get card_id from URL path
        card_id = request.view_args.get('card_id')
        
        if not card_id:
            return jsonify({
                "status": "error",
                "error": "missing_card_id",
                "readable": "Не указан ID карты"
            }), 400
        
        # Mock logic - return the same card with the requested ID
        card_data = MOCK_CARD.copy()
        card_data["id"] = card_id
        
        return jsonify({
            "status": "ok",
            "card": card_data
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
