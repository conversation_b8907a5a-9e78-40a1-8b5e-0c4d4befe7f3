"""
Tags endpoint
/api/tags
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from run import GET

# Mock tags data
MOCK_TAGS = [
    {
        "id": 1,
        "name": "test_tag",
        "color": "#FF5733",
        "cards_count": 15
    },
    {
        "id": 2,
        "name": "production",
        "color": "#33FF57",
        "cards_count": 8
    },
    {
        "id": 3,
        "name": "usa",
        "color": "#3357FF",
        "cards_count": 12
    },
    {
        "id": 4,
        "name": "europe",
        "color": "#FF33A1",
        "cards_count": 6
    },
    {
        "id": 5,
        "name": "testing",
        "color": "#A133FF",
        "cards_count": 3
    }
]

@GET
def get(request):
    """Get all tags"""
    try:
        return jsonify({
            "status": "ok",
            "tags": MOCK_TAGS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
