#!/usr/bin/env python3
"""
API Testing Script
Tests basic endpoints for 200 status codes
"""

import requests
import json
import time
import sys
from typing import Dict, List, Tuple

# Base URL for the API
BASE_URL = "http://localhost:5000"

# Test endpoints with their expected methods and sample data
TEST_ENDPOINTS = [
    # Health check
    ("GET", "/api/health", None),

    # Auth endpoints
    ("POST", "/api/auth/login", {
        "login": "test",
        "password": "test",
        "is_remember": True
    }),
    ("POST", "/api/auth/register", {
        "name": "Test User",
        "email": "<EMAIL>",
        "password": "password123",
        "social": "telegram",
        "social_username": "testuser",
        "team_code": "TEAM123"
    }),
    ("POST", "/api/auth/forgot_password", {
        "email": "<EMAIL>"
    }),

    # Account endpoints
    ("GET", "/api/account/info", None),
    ("GET", "/api/account/update_info", None),
    ("POST", "/api/account/update_password", {
        "new_password": "newpass123",
        "new_password_reenter": "newpass123"
    }),
    ("GET", "/api/account/notifications", None),
    ("GET", "/api/account/actions", None),
    ("GET", "/api/account/logs", None),
    ("GET", "/api/account/balances", None),
    ("GET", "/api/account/bins", None),
    ("GET", "/api/account/bot_notifications", None),
    ("GET", "/api/account/3ds", None),

    # Cards endpoints
    ("POST", "/api/cards", {
        "title": "test",
        "page": 1,
        "per_page": 10
    }),
    ("GET", "/api/cards/14", None),
    ("POST", "/api/cards/order", {
        "bin": "411123",
        "amount": 1,
        "topup": 100.0
    }),
    ("POST", "/api/cards/topup", {
        "card_id": 14,
        "from_balance": 1,
        "amount": 50.0
    }),

    # Tags endpoints
    ("GET", "/api/tags", None),
    ("POST", "/api/tags/create", {
        "name": "test_tag",
        "color": "#FF5733"
    }),

    # Transactions endpoints
    ("POST", "/api/transactions", {
        "page": 1,
        "per_page": 10
    }),
    ("GET", "/api/transactions/1", None),

    # Service endpoints
    ("GET", "/api/service/rules", None),
    ("POST", "/api/service/rules", {
        "version": "1.2"
    }),
    ("POST", "/api/service/rules/accept", {
        "version": "1.2"
    }),
    ("GET", "/api/service/news", None),
]

def test_endpoint(method: str, endpoint: str, data: dict = None) -> Tuple[bool, int, str]:
    """Test a single endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=10)
        elif method == "POST":
            headers = {"Content-Type": "application/json"}
            response = requests.post(url, json=data, headers=headers, timeout=10)
        else:
            return False, 0, f"Unsupported method: {method}"
        
        return True, response.status_code, response.text[:200] + "..." if len(response.text) > 200 else response.text
        
    except requests.exceptions.ConnectionError:
        return False, 0, "Connection refused - is the server running?"
    except requests.exceptions.Timeout:
        return False, 0, "Request timeout"
    except Exception as e:
        return False, 0, f"Error: {str(e)}"

def wait_for_server(max_wait: int = 30) -> bool:
    """Wait for server to be ready"""
    print(f"⏳ Waiting for server to start (max {max_wait}s)...")
    
    for i in range(max_wait):
        try:
            response = requests.get(f"{BASE_URL}/api/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                return True
        except:
            pass
        
        time.sleep(1)
        if i % 5 == 4:
            print(f"   Still waiting... ({i+1}s)")
    
    print("❌ Server did not start in time")
    return False

def main():
    """Main testing function"""
    print("🧪 API Testing Script")
    print("=" * 50)
    
    # Check if server is running
    if not wait_for_server():
        print("\n💡 To start the server, run: python run.py")
        sys.exit(1)
    
    print(f"\n🎯 Testing {len(TEST_ENDPOINTS)} endpoints...")
    print("-" * 50)
    
    results = []
    passed = 0
    failed = 0
    
    for method, endpoint, data in TEST_ENDPOINTS:
        print(f"Testing {method} {endpoint}...", end=" ")
        
        success, status_code, response_text = test_endpoint(method, endpoint, data)
        
        if success:
            if status_code == 200:
                print(f"✅ {status_code}")
                passed += 1
                results.append((endpoint, method, status_code, "PASS", ""))
            else:
                print(f"❌ {status_code}")
                failed += 1
                results.append((endpoint, method, status_code, "FAIL", response_text[:100]))
        else:
            print(f"💥 ERROR")
            failed += 1
            results.append((endpoint, method, 0, "ERROR", response_text))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    for endpoint, method, status, result, error in results:
        status_icon = "✅" if result == "PASS" else "❌" if result == "FAIL" else "💥"
        print(f"{status_icon} {method:4} {endpoint:30} {status:3} {result}")
        if error and result != "PASS":
            print(f"    └─ {error}")
    
    print(f"\n📈 Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed. Check the server logs for details.")
        return 1

def test_json_responses():
    """Test that responses are valid JSON"""
    print("\n🔍 Testing JSON response format...")
    
    test_cases = [
        ("GET", "/api/health"),
        ("GET", "/api/service/rules"),
        ("POST", "/api/auth/login", {"login": "test", "password": "test"})
    ]
    
    for method, endpoint, *data in test_cases:
        url = f"{BASE_URL}{endpoint}"
        
        try:
            if method == "GET":
                response = requests.get(url)
            else:
                response = requests.post(url, json=data[0] if data else None, 
                                       headers={"Content-Type": "application/json"})
            
            # Try to parse JSON
            json_data = response.json()
            
            # Check for required fields
            if "status" in json_data:
                print(f"✅ {method} {endpoint} - Valid JSON with status field")
            else:
                print(f"⚠️  {method} {endpoint} - JSON missing 'status' field")
                
        except json.JSONDecodeError:
            print(f"❌ {method} {endpoint} - Invalid JSON response")
        except Exception as e:
            print(f"💥 {method} {endpoint} - Error: {e}")

if __name__ == "__main__":
    exit_code = main()
    
    # Additional JSON format tests
    test_json_responses()
    
    print(f"\n🏁 Testing complete. Exit code: {exit_code}")
    sys.exit(exit_code)
