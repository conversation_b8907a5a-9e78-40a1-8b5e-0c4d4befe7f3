"""
Account bins endpoint
/api/account/bins
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock bins data
MOCK_BINS = [
    {
        "id": 14,
        "bin": "412222",
        "card_type": "balanced",
        "linked_balance_id": 14,
        "available": 124.00,
        "currency": "USD",
        "comment": "Основной бин для США",
        "commission": {
            "creation": 2.50,
            "topup": 0.5,
            "transaction": 0.3
        }
    },
    {
        "id": 15,
        "bin": "555444",
        "card_type": "linked",
        "linked_balance_id": 15,
        "available": 89.50,
        "currency": "EUR",
        "comment": "Европейский бин",
        "commission": {
            "creation": 3.00,
            "topup": 0.7,
            "transaction": 0.4
        }
    },
    {
        "id": 16,
        "bin": "444333",
        "card_type": "static",
        "linked_balance_id": 16,
        "available": 1500.00,
        "currency": "USD",
        "comment": "Премиум бин с низкими комиссиями",
        "commission": {
            "creation": 1.00,
            "topup": 0.2,
            "transaction": 0.1
        }
    }
]

@GET
def get(request):
    """Get available bins"""
    try:
        return jsonify({
            "status": "ok",
            "bins": MOCK_BINS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
