"""
Account bot notification settings endpoint
/api/account/bot_notification_settings
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional, Dict, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET, POST

class NotificationSetting(BaseModel):
    is_enabled: bool
    content: str

class UpdateNotificationSettingsRequest(BaseModel):
    notifications: Dict[str, NotificationSetting]

# Mock notification settings
MOCK_NOTIFICATION_SETTINGS = {
    "new_card_issue": {
        "is_enabled": True,
        "content": "Новая карта выпущена: {card_number}, срок действия: {card_exp}, CVV: {card_cvv}",
        "available_aliases": ["card_number", "card_exp", "card_cvv"]
    },
    "transaction_decline": {
        "is_enabled": True,
        "content": "Транзакция отклонена на карте {card_number} на сумму {amount} {currency}",
        "available_aliases": ["card_number", "amount", "currency", "merchant"]
    },
    "card_frozen": {
        "is_enabled": False,
        "content": "Карта {card_number} заморожена",
        "available_aliases": ["card_number", "reason"]
    },
    "balance_low": {
        "is_enabled": True,
        "content": "Низкий баланс на карте {card_number}: {balance} {currency}",
        "available_aliases": ["card_number", "balance", "currency"]
    }
}

@GET
def get(request):
    """Get bot notification settings"""
    try:
        return jsonify({
            "status": "ok",
            "notifications": MOCK_NOTIFICATION_SETTINGS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500

@POST
def post(request):
    """Update bot notification settings"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            update_request = UpdateNotificationSettingsRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock update logic
        return jsonify({
            "status": "ok"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
