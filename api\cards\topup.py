"""
Cards topup endpoint
/api/cards/topup
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class TopupRequest(BaseModel):
    card_id: int
    from_balance: int
    amount: float

@POST
def post(request):
    """Topup card"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            topup_request = TopupRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock topup logic
        return jsonify({
            "status": "ok",
            "task_id": 15,
            "tasks": [
                {
                    "uid": "e8a98690-da79-49cd-ba1d-893ed8162795",
                    "card_id": topup_request.card_id,
                    "balance_id": topup_request.from_balance,
                    "amount": topup_request.amount,
                    "action": "topup",
                    "status": "processing"
                }
            ]
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
