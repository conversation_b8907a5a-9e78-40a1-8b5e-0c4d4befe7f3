"""
Account logs endpoint
/api/account/logs
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock logs data
MOCK_LOGS = [
    {
        "id": 1,
        "timestamp": **********,
        "user": "admin",
        "block": "account",
        "operation": "update_info",
        "from_value": {"name": "Old Name"},
        "to_value": {"name": "New Name"},
        "status": "success"
    },
    {
        "id": 2,
        "timestamp": **********,
        "user": "user123",
        "block": "cards",
        "operation": "freeze",
        "from_value": {"status": "active"},
        "to_value": {"status": "frozen"},
        "status": "success"
    },
    {
        "id": 3,
        "timestamp": **********,
        "user": "system",
        "block": "balance",
        "operation": "topup",
        "from_value": {"balance": 100.00},
        "to_value": {"balance": 600.00},
        "status": "success"
    },
    {
        "id": 4,
        "timestamp": **********,
        "user": "admin",
        "block": "cards",
        "operation": "limit_update",
        "from_value": {"limit": 500.00},
        "to_value": {"limit": 1000.00},
        "status": "failed"
    }
]

@GET
def get(request):
    """Get account logs"""
    try:
        return jsonify({
            "status": "ok",
            "logs": MOCK_LOGS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
