"""
Cards order endpoint
/api/cards/order
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class OrderRequest(BaseModel):
    bin: str
    amount: int
    topup: Optional[float] = None

@POST
def post(request):
    """Order new cards"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            order_request = OrderRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock order logic
        tasks = []
        for i in range(order_request.amount):
            tasks.append({
                "uid": f"e8a98690-da79-49cd-ba1d-893ed8162{794 + i}",
                "action": "new_card",
                "card_price": 2.50,
                "topup": order_request.topup or 0.00,
                "status": "processing"
            })
        
        return jsonify({
            "status": "ok",
            "task_id": 14,
            "tasks": tasks
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
