"""
Account update password endpoint
/api/account/update_password
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class UpdatePasswordRequest(BaseModel):
    new_password: str
    new_password_reenter: str

@POST
def post(request):
    """Update account password"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            update_request = UpdatePasswordRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Check password match
        if update_request.new_password != update_request.new_password_reenter:
            return jsonify({
                "status": "error",
                "error": "password_mismatch",
                "readable": "Пароли не совпадают"
            }), 400
        
        # Mock password update logic
        return jsonify({
            "status": "ok"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
