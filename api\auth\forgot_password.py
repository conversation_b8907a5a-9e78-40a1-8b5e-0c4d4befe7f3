"""
Auth forgot password endpoint
/api/auth/forgot_password
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class ForgotPasswordRequest(BaseModel):
    email: str

@POST
def post(request):
    """Forgot password"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            forgot_request = ForgotPasswordRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # <PERSON><PERSON> forgot password logic
        return jsonify({
            "status": "ok",
            "recovery_code": "ABC123",
            "sent_email": True,
            "next_retry": 1723432423
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
