## Общая информация

### Запросы

Запросы шлются на указанную базовую ссылку. 

Все запросы с авторизацией должны иметь

```markdown
Authorization: Bearer <токен>
Content-Type: application/json
Accept: application/json
```

### Статусы

При успешном `200 OK` . Контент в JSON 

При ошибке: 

`400 BAD REQUEST`  - При ошибке данных

`401 UNAUTH` - Неверная авторизация

`403 FORBIDDEN` - Недоступные данные

При всех случаях вернется JSON ввиде

```json
{
	"status": "error",
	"error": "system_error",
	"readeable": "Системная ошибка"
}
```

## Авторизация

### Вход в аккаунт

`POST auth/login` - Авторизация по логину и паролю

Запрос

```json
{
	"login": "login",
	"password": "password",
	"is_remember": true,
}
```

Ответ:

```json
{
	"status": "ok",
	"access_token": "...",
	"valid_to": ***********, // UNIX timestamp UTC+0
}
```

### Регистрация

`POST auth/register`

Запрос

```json
{
	"name": "test"
	"email": "<EMAIL>",
	"password": "test",
	"social": "telegram",
	"social_username": "test"
	"team_code": "test"
}
```

Ответ:

```json
{
	"status": "ok",
	"sent_email": true,
	"retry_code": "..." // Код для перезапроса кода с почты
	"next_retry": ********** // UNIX timestamp UTC+0
}
```

### Повтор запроса кода подтверждения

`POST auth/retry`

Запрос

```json
{
	"retry_code": "test"
	"email": "<EMAIL>",
}
```

Ответ:

```json
{
	"status": "ok",
	"email": "<EMAIL>",
	"sent_email": true,
	"retry_code": "..." // Код для перезапроса кода с почты
	"next_retry": ********** // UNIX timestamp UTC+0
}
```

### Восстановление пароля

`POST auth/forgot_password`

Запрос

```json
{
	"email": "<EMAIL>"
}
```

Ответ:

```json
{
	"status": "ok",
	"recovery_code": "ABC123", // Код восстановления
	"sent_email": true,
	"next_retry": ********** // UNIX timestamp UTC+0
}
```

### Верификация восстановления пароля

`POST auth/verify_recovery`

Запрос

```json
{
	"recovery_code": "ABC123",
	"new_password": "new_password",
	"new_password_reenter": "new_password"
}
```

Ответ:

```json
{
	"status": "ok",
	"access_token": "...",
	"valid_to": *********** // UNIX timestamp UTC+0
}
```

## Аккаунт

### Общая информация

`GET account/info`

Ответ:

```json
{
	"status": "ok"
	"name": "Mark Markov",
	"spend": 12412.32,
	"holds": 1234.32
	"cards": 141,
	"decline_rate": 12.23
	
	"team": {
		"id": 15,
		"title": "TEST TEAM",
		"role": "buyer", // admin, financier, teamlead, buyer
	}
	
	"manager": {
		"name": "Anton",
		"social": "telegram"
		"social_username": "anton"
	},
	
	"account_level": "premium", // basic, premium, vip

	"charts": {
		"spend": [1,2,3,4] ... // Каждый день. 30 дней
		"by_transaction_type": { // Транзакции по типам. (MCC?)
			"Google": 151,
			"Facebook": 155,
		}
	},
	
	"banners": [ // Баннеры на главной странице
		{
			"timestamp": ********,
			"priority": "high"
			"title": "Ваш аккаунт заморожен",
			"content": "...",
			"is_dismissable": false // Можно ли скрыть
		}
	]
	
}
```

### Обновление пароля

`POST account/update_password`

Запрос

```json
{
	"new_password": "test",
	"new_password_reenter": "test"
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### Обновление информации аккаунта

`POST account/update_info`

Запрос

```json
{
	"name": "New Name",
	"notifications": {
		"email": true,
		"push": false,
		"telegram": true
	},
	"two_fa": {
		"enabled": true,
		"qr_code": "base64:image_data" // QR код для настройки 2FA
	},
	"api_key": "new_api_key",
	"session_limit": "single", // "single" или "multiple"
	"card_visibility": true // показывать ли данные карт
}
```

Ответ:

```json
{
	"status": "ok"
}
```

`GET account/update_info`

Ответ:

```json
{
	"status": "ok",
	"name": "Current Name",
	"notifications": {
		"email": true,
		"push": false,
		"telegram": true
	},
	"two_fa": {
		"enabled": true,
		"qr_code": "base64:image_data"
	},
	"api_key": "current_api_key",
	"session_limit": "single",
	"card_visibility": true
}
```

### Уведомления

`GET account/notifications`

Ответ:

```json
{
	"status": "ok",
	
	"notifications": [
		{
			"id": 14,
			"datetime": **********
			"priority": "high", // "low", "normal"
			"title": "Аккаунт заморожен",
			"content": "Ваш аккаунт заморожен."
		},
		...
	]
	
}
```

`GET account/actions`

Ответ:

```json
{
	"status": "ok",

	"actions": [
		{
			"id": 14,
			"datetime": **********
			"action_on": "cards", // "account", "balance",
			"action": "freeze",
			"cards": [
				{
					"id": 14,
					"title": "Test Card",
					"card_number": "4111 **** **** 1234"
				}, ...
			],
			"notes": ""
		},
		...
	]

}
```

### Логи аккаунта

`GET account/logs`

Ответ:

```json
{
	"status": "ok",
	"logs": [
		{
			"id": 1,
			"timestamp": **********,
			"user": "admin",
			"block": "account",
			"operation": "update_info",
			"from_value": {"name": "Old Name"},
			"to_value": {"name": "New Name"},
			"status": "success"
		},
		{
			"id": 2,
			"timestamp": **********,
			"user": "user123",
			"block": "cards",
			"operation": "freeze",
			"from_value": {"status": "active"},
			"to_value": {"status": "frozen"},
			"status": "success"
		}
	]
}
```

### Балансы

`GET account/balances`

Ответ:

```json
{
	"status": "ok",
	
	"balances": [
	
		{
			"id": 14,
			"title": "USD Balance",
			"type": "system",
			"balance": 124.00,
			"hold": 0.00,
			"available": 124.00
			"currency": "USD"	
		},
		...
		
	]
	
}
```

### Пополнение аккаунта

`POST account/topup`

```json
{
	"type": "USDT - TRC20",
	"args": [
		"txid",
	],
	"amount": 500.00,
	"to_balance_id": 1
}
```

Ответ

```json
{
	"status": "ok",
	...
}
```

### Вывод средств из аккаунта

`POST account/withdraw`

```json
{
	"type": "USDT - TRC20",
	"args": [
		"0x0_wallet_address",
	],
	"amount": 500.00,
	"from_balance_id": 1
}
```

Ответ

```json
{
	"status": "ok",
	...
}
```

### Доступные бины

`GET account/bins`

```json
{
	"status": "ok",
	"bins": [
		{
			"id": 14,
			"bin": "412222",
			"card_type": "balanced",
			"linked_balance_id": 14,
			"available": 124.00,
			"currency": "USD",
			"comment": "Основной бин для США",
			"commission": {
				"creation": 2.50, // Разовая комиссия создания
				"topup": 0.5, // Комиссия при пополнении (%)
				"transaction": 0.3 // Комиссия при транзакции (%)
			}
		},
		...
	]
}
```

## Карты

### Список карт

`POST cards`

Запрос

Все поля опциональны. 

```json
{
	"title": "card" // 	название карты
	"lasts": "1234" // 	последние цифры
	"starts": "4111" // первые цифры (бин)
	"balance_more": 200 // баланс больше 200
	"balance_less": 500 // баланс меньше 500
	"card_type": "" // balanced, linked, static
	"card_scheme": "visa" // mastercard, unionpay, amex
	"tags": ["tag1", "tag2"] // поиск по тегам
	"order_by": "order_time" // last_use, order_time, title, spend, hold, decline_rate, transactions
	"order": "desc" // "asc"
	"excel": false // Выдаст результат как таблицу excel
}
```

type = freeze, block, actual

Ответ:

```json
{
	"status": "ok",
	"card_count": 523,
	"per_page": 100,
	"page": 1,
	"max_page": 5
	"cards": [
	 {
		 "id": 14,
		 "title": "Test card",
		 "notes": "",
		 "tags": ["test_tags", "test2"],
		 "number": "****************",
		 "exp_date": "03/25",
		 "cvv": "123",
		 "ordered_time": **********,
		 "issued_time": **********,
		 "last_transaction": 17456456452,
		 "card_type": "balanced",
		 "balance_id": -1,
		 "balance": 500,
		 "limit": 500,
		 "scheme": "visa"
		 "can_topup": true,
		 "spend": 123.42,
		 "hold": 0.00,
		 "decline_rate": 0.00,
		 "daily_spend": 50.00,
		 "total_spend": 123.42,
		 "daily_hold": 10.00,
		 "total_hold": 0.00
	 }
	],
}
```

### Информация об одной карте

`GET cards/{card_id}`

Ответ:

```json
{
	"status": "ok",
	"card": {
		"id": 14,
		"title": "Test card",
		"notes": "",
		"tags": ["test_tags", "test2"],
		"number": "****************",
		"exp_date": "03/25",
		"cvv": "123",
		"ordered_time": **********,
		"issued_time": **********,
		"last_transaction": 17456456452,
		"card_type": "balanced",
		"balance_id": -1,
		"balance": 500,
		"limit": 500,
		"scheme": "visa",
		"can_topup": true,
		"spend": 123.42,
		"hold": 0.00,
		"decline_rate": 0.00,
		"daily_spend": 50.00,
		"total_spend": 123.42,
		"daily_hold": 10.00,
		"total_hold": 0.00,
		"status": "active" // active, frozen, blocked
	}
}
```

### Заказ карт

`POST cards/order`

Запрос

```json
{
	"bin": "411123",
	"amount": 1,
	"topup": 140 // Необязательное поле, только для карт с балансом
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "action": "new_card",
		 "card_price": 0.00,
		 "topup": 140.00
		 "status": "processing"
	 }
	],
}
```

### Пополнение карт

`POST cards/topup`

Запрос

```json
{
	"card_id": 14
	"from_balance": 14,
	"amount": 150
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "balance_id": 14
		 "amount": 150
		 "action": "topup",
		 "status": "processing"
	 }
	],
}
```

### Изменение общей информации карты

`POST cards/change/info`

Запрос

```json
{
	"card_id": 14
	"title": "test card",
	"notes": "",
	"tags": [""],
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "info",
		 "status": "processing"
	 }
	],
}
```

### Изменение лимиты карты

`POST cards/change/limit`

Запрос

```json
{
	"card_id": 14
	"new_limit": 1400.00
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "limit",
		 "new_limit": 1400.00
		 "status": "processing"
	 }
	],
}
```

### Замораживание карты

`POST cards/change/freeze`

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "freeze",
		 "status": "processing"
	 }
	],
}
```

### Размораживание карты

`POST cards/change/unfreeze`

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "unfreeze",
		 "status": "processing"
	 }
	],
}
```

### Блокировка карты

`POST cards/change/block` 

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "block",
		 "status": "processing"
	 }
	],
}
```

## Управление тегами

### Получение всех тегов

`GET tags`

Ответ:

```json
{
	"status": "ok",
	"tags": [
		{
			"id": 1,
			"name": "test_tag",
			"color": "#FF5733",
			"cards_count": 15
		},
		{
			"id": 2,
			"name": "production",
			"color": "#33FF57",
			"cards_count": 8
		}
	]
}
```

### Создание тега

`POST tags/create`

Запрос

```json
{
	"name": "new_tag",
	"color": "#3357FF"
}
```

Ответ:

```json
{
	"status": "ok",
	"tag": {
		"id": 3,
		"name": "new_tag",
		"color": "#3357FF",
		"cards_count": 0
	}
}
```

### Обновление тега

`POST tags/update`

Запрос

```json
{
	"tag_id": 3,
	"name": "updated_tag",
	"color": "#FF33A1"
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### Удаление тега

`POST tags/delete`

Запрос

```json
{
	"tag_id": 3
}
```

Ответ:

```json
{
	"status": "ok"
}
```

## Транзакции

### Список транзакций

`POST transactions`

Запрос

Все поля опциональны.

```json
{
	"card_id": 14, // фильтр по карте
	"date_from": **********, // UNIX timestamp
	"date_to": **********, // UNIX timestamp
	"is_decline": false, // только отклоненные/успешные
	"amount_from": 10.00, // сумма от
	"amount_to": 500.00, // сумма до
	"currency": "USD", // валюта
	"tags": ["tag1", "tag2"], // фильтр по тегам карт
	"order_by": "date", // date, amount, status
	"order": "desc", // asc
	"page": 1,
	"per_page": 50
}
```

Ответ:

```json
{
	"status": "ok",
	"transaction_count": 1523,
	"per_page": 50,
	"page": 1,
	"max_page": 31,
	"transactions": [
		{
			"id": 1,
			"date": **********,
			"is_decline": false,
			"card": {
				"id": 14,
				"number": "4111 **** **** 1234",
				"name": "Test Card"
			},
			"card_tags": ["production", "usa"],
			"card_name": "Test Card",
			"charge_amount": 100.50,
			"charge_currency": "USD",
			"requested_amount": 100.50,
			"requested_currency": "USD",
			"last_update": **********,
			"last_status": "approved",
			"details": "Payment to Google Ads",
			"card_owner_id": 123
		}
	]
}
```

### Информация об одной транзакции

`GET transactions/{transaction_id}`

Ответ:

```json
{
	"status": "ok",
	"transaction": {
		"id": 1,
		"date": **********,
		"is_decline": false,
		"card": {
			"id": 14,
			"number": "4111 **** **** 1234",
			"name": "Test Card"
		},
		"card_tags": ["production", "usa"],
		"card_name": "Test Card",
		"charge_amount": 100.50,
		"charge_currency": "USD",
		"requested_amount": 100.50,
		"requested_currency": "USD",
		"last_update": **********,
		"last_status": "approved",
		"details": "Payment to Google Ads",
		"card_owner_id": 123
	}
}
```

## Уведомления бота

### Получение списка чатов бота

`GET account/bot_notifications`

Ответ:

```json
{
	"status": "ok",
	"chats": [
		{
			"id": 1,
			"chat_id": "*********",
			"chat_name": "Main Chat",
			"is_enabled": true,
			"created_at": **********
		},
		{
			"id": 2,
			"chat_id": "*********",
			"chat_name": "Backup Chat",
			"is_enabled": false,
			"created_at": **********
		}
	]
}
```

### Создание чата бота

`POST account/bot_notifications/create`

Запрос

```json
{
	"chat_id": "*********",
	"chat_name": "New Chat",
	"is_enabled": true
}
```

Ответ:

```json
{
	"status": "ok",
	"chat": {
		"id": 3,
		"chat_id": "*********",
		"chat_name": "New Chat",
		"is_enabled": true,
		"created_at": **********
	}
}
```

### Обновление чата бота

`POST account/bot_notifications/update`

Запрос

```json
{
	"id": 1,
	"chat_name": "Updated Chat Name",
	"is_enabled": false
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### Удаление чата бота

`POST account/bot_notifications/delete`

Запрос

```json
{
	"id": 1
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### Настройки уведомлений бота

`GET account/bot_notification_settings`

Ответ:

```json
{
	"status": "ok",
	"notifications": {
		"new_card_issue": {
			"is_enabled": true,
			"content": "Новая карта выпущена: {card_number}, срок действия: {card_exp}, CVV: {card_cvv}",
			"available_aliases": ["card_number", "card_exp", "card_cvv"]
		},
		"transaction_decline": {
			"is_enabled": true,
			"content": "Транзакция отклонена на карте {card_number} на сумму {amount} {currency}",
			"available_aliases": ["card_number", "amount", "currency", "merchant"]
		},
		"card_frozen": {
			"is_enabled": false,
			"content": "Карта {card_number} заморожена",
			"available_aliases": ["card_number", "reason"]
		}
	}
}
```

`POST account/bot_notification_settings`

Запрос

```json
{
	"notifications": {
		"new_card_issue": {
			"is_enabled": true,
			"content": "Новая карта: {card_number}"
		},
		"transaction_decline": {
			"is_enabled": false,
			"content": "Отклонена транзакция: {amount} {currency}"
		}
	}
}
```

Ответ:

```json
{
	"status": "ok"
}
```

## Сервисные функции

### Новости

`GET service/news`

Ответ:

```json
{
	"status": "ok",
	"news": [
		{
			"id": 1,
			"header": "Обновление системы",
			"date": **********,
			"is_read": false,
			"content": "Сегодня мы обновили нашу систему...",
			"image": "https://example.com/image.jpg"
		},
		{
			"id": 2,
			"header": "Новые возможности",
			"date": **********,
			"is_read": true,
			"content": "Добавлены новые функции...",
			"image": null
		}
	]
}
```

### Правила сервиса

`GET service/rules`

Ответ:

```json
{
	"status": "ok",
	"rules": {
		"content": "Правила использования сервиса...",
		"is_accepted": true,
		"last_updated": **********,
		"version": "1.2"
	}
}
```

`POST service/rules/accept`

Запрос

```json
{
	"version": "1.2"
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### 3DS информация

`GET account/3ds`

Запрос (все поля опциональны)

```json
{
	"card_id": 14 // фильтрация по карте
}
```

Ответ:

```json
{
	"status": "ok",
	"3ds_records": [
		{
			"id": 1,
			"card_id": 14,
			"date": **********,
			"content": "3DS authentication successful for transaction #12345"
		},
		{
			"id": 2,
			"card_id": 14,
			"date": **********,
			"content": "3DS challenge presented to cardholder"
		},
		{
			"id": 3,
			"card_id": 15,
			"date": **********,
			"content": "3DS authentication failed - timeout"
		}
	]
}
```

`GET cards/{card_id}/3ds`

Ответ:

```json
{
	"status": "ok",
	"3ds_records": [
		{
			"id": 1,
			"card_id": 14,
			"date": **********,
			"content": "3DS authentication successful for transaction #12345"
		},
		{
			"id": 2,
			"card_id": 14,
			"date": **********,
			"content": "3DS challenge presented to cardholder"
		}
	]
}
```