## Общая информация

### Запросы

Запросы шлются на указанную базовую ссылку. 

Все запросы с авторизацией должны иметь

```markdown
Authorization: Bearer <токен>
Content-Type: application/json
Accept: application/json
```

### Статусы

При успешном `200 OK` . Контент в JSON 

При ошибке: 

`400 BAD REQUEST`  - При ошибке данных

`401 UNAUTH` - Неверная авторизация

`403 FORBIDDEN` - Недоступные данные

При всех случаях вернется JSON ввиде

```json
{
	"status": "error",
	"error": "system_error",
	"readeable": "Системная ошибка"
}
```

## Авторизация

### Вход в аккаунт

`POST auth/login` - Авторизация по логину и паролю

Запрос

```json
{
	"login": "login",
	"password": "password",
	"is_remember": true,
}
```

Ответ:

```json
{
	"status": "ok",
	"access_token": "...",
	"valid_to": 17345678534, // UNIX timestamp UTC+0
}
```

### Регистрация

`POST auth/register`

Запрос

```json
{
	"name": "test"
	"email": "<EMAIL>",
	"password": "test",
	"social": "telegram",
	"social_username": "test"
	"team_code": "test"
}
```

Ответ:

```json
{
	"status": "ok",
	"sent_email": true,
	"retry_code": "..." // Код для перезапроса кода с почты
	"next_retry": ********** // UNIX timestamp UTC+0
}
```

### Повтор запроса кода подтверждения

`POST auth/retry`

Запрос

```json
{
	"retry_code": "test"
	"email": "<EMAIL>",
}
```

Ответ:

```json
{
	"status": "ok",
	"email": "<EMAIL>",
	"sent_email": true,
	"retry_code": "..." // Код для перезапроса кода с почты
	"next_retry": ********** // UNIX timestamp UTC+0
}
```

## Аккаунт

### Общая информация

`GET account/info`

Ответ:

```json
{
	"status": "ok"
	"name": "Mark Markov",
	"spend": 12412.32,
	"holds": 1234.32
	"cards": 141,
	"decline_rate": 12.23
	
	"team": {
		"id": 15,
		"title": "TEST TEAM",
		"role": "buyer", // admin, financier, teamlead, buyer
	}
	
	"manager": {
		"name": "Anton",
		"social": "telegram"
		"social_username": "anton"
	},
	
	"charts": {
		"spend": [1,2,3,4] ... // Каждый день. 30 дней
		"by_transaction_type": { // Транзакции по типам. (MCC?)
			"Google": 151,
			"Facebook": 155,
		}
	},
	
	"banners": [ // Баннеры на главной странице
		{
			"timestamp": ********,
			"priority": "high"
			"title": "Ваш аккаунт заморожен",
			"content": "...",
			"is_dismissable": false // Можно ли скрыть
		}
	]
	
}
```

### Обновление пароля

`POST account/update_password`

Запрос

```json
{
	"new_password": "test",
	"new_password_reenter": "test"
}
```

Ответ:

```json
{
	"status": "ok"
}
```

### Обновление информации аккаунта

`POST account/update_info`

???

### Уведомления

`GET account/notifications`

Ответ:

```json
{
	"status": "ok",
	
	"notifications": [
		{
			"id": 14,
			"datetime": **********
			"priority": "high", // "low", "normal"
			"title": "Аккаунт заморожен",
			"content": "Ваш аккаунт заморожен."
		},
		...
	]
	
}
```

`GET account/actions`

Ответ:

```json
{
	"status": "ok",
	
	"actions": [
		{
			"id": 14,
			"datetime": **********
			"action_on": "cards", // "account", "balance",
			"action": "freeze",
			"cards": [
				{
					"id": 14,
					"title": "Test Card",
					"card_number": "4111 **** **** 1234"
				}, ...
			],
			"notes": ""
		},
		...
	]
	
}
```

### Балансы

`GET account/balances`

Ответ:

```json
{
	"status": "ok",
	
	"balances": [
	
		{
			"id": 14,
			"title": "USD Balance",
			"type": "system",
			"balance": 124.00,
			"hold": 0.00,
			"available": 124.00
			"currency": "USD"	
		},
		...
		
	]
	
}
```

### Пополнение аккаунта

`POST account/topup`

```json
{
	"type": "USDT - TRC20",
	"args": [
		"txid",
	],
	"amount": 500.00,
	"to_balance_id": 1
}
```

Ответ

```json
{
	"status": "ok",
	...
}
```

### Вывод средств из аккаунта

`POST account/withdraw`

```json
{
	"type": "USDT - TRC20",
	"args": [
		"0x0_wallet_address",
	],
	"amount": 500.00,
	"from_balance_id": 1
}
```

Ответ

```json
{
	"status": "ok",
	...
}
```

### Доступные бины

`GET account/bins`

```json
{
	"status": "ok",
	"bins": [
		{
			"id": 14,
			"bin": "412222"
			"card_type": "balanced",
			"linked_balance_id": 14,
			"available": 124.00
			"currency": "USD"
		},
		...
	]
}
```

add comission for bins

## Карты

### Список карт

`POST cards`

Запрос

Все поля опциональны. 

```json
{
	"title": "card" // 	название карты
	"lasts": "1234" // 	последние цифры 
	"starts": "4111" // первые цифры (бин)
	"balance_more": 200 // баланс больше 200
	"balance_less": 500 // баланс меньше 500
	"card_type": "" // balanced, linked, static
	"card_scheme": "visa" // mastercard, unionpay, amex
	"order_by": "order_time" // last_use, order_time, title, spend, hold, decline_rate, transactions
	"order": "desc" // "asc"
	"excel": false // Выдаст результат как таблицу excel
}
```

type = freeze, block, actual

Ответ:

```json
{
	"status": "ok",
	"card_count": 523,
	"per_page": 100,
	"page": 1,
	"max_page": 5
	"cards": [
	 {
		 "id": 14,
		 "title": "Test card",
		 "notes": "",
		 "tags": ["test_tags", "test2"],
		 "number": "****************",
		 "exp_date": "03/25",
		 "cvv": "123",
		 "ordered_time": **********,
		 "issued_time": **********,
		 "last_transaction": 17456456452,
		 "card_type": "balanced",
		 "balance_id": -1,
		 "balance": 500,
		 "limit": 500,
		 "scheme": "visa"
		 "can_topup": true,
		 "spend": 123.42,
		 "hold": 0.00,
		 "decline_rate": 0.00
	 }
	],
}
```

### Заказ карт

`POST cards/order`

Запрос

```json
{
	"bin": "411123",
	"amount": 1,
	"topup": 140 // Необязательное поле, только для карт с балансом
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "action": "new_card",
		 "card_price": 0.00,
		 "topup": 140.00
		 "status": "processing"
	 }
	],
}
```

### Пополнение карт

`POST cards/topup`

Запрос

```json
{
	"card_id": 14
	"from_balance": 14,
	"amount": 150
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "balance_id": 14
		 "amount": 150
		 "action": "topup",
		 "status": "processing"
	 }
	],
}
```

### Изменение общей информации карты

`POST cards/change/info`

Запрос

```json
{
	"card_id": 14
	"title": "test card",
	"notes": "",
	"tags": [""],
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "info",
		 "status": "processing"
	 }
	],
}
```

### Изменение лимиты карты

`POST cards/change/limit`

Запрос

```json
{
	"card_id": 14
	"new_limit": 1400.00
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "limit",
		 "new_limit": 1400.00
		 "status": "processing"
	 }
	],
}
```

### Замораживание карты

`POST cards/change/freeze`

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "freeze",
		 "status": "processing"
	 }
	],
}
```

### Размораживание карты

`POST cards/change/unfreeze`

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "unfreeze",
		 "status": "processing"
	 }
	],
}
```

### Блокировка карты

`POST cards/change/block` 

Запрос

```json
{
	"card_id": 14
}
```

Ответ:

```json
{
	"status": "ok",
	"task_id": 14
	"tasks": [
	 {
		 "uid": "e8a98690-da79-49cd-ba1d-893ed8162794",
		 "card_id" : 14,
		 "action": "block",
		 "status": "processing"
	 }
	],
}
```