#!/usr/bin/env python3
"""
Route validation script
Checks if all endpoints from docs.md are implemented in api/ directory
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Set

def extract_endpoints_from_docs() -> Dict[str, List[str]]:
    """Extract all endpoints from docs.md"""
    endpoints = {}
    
    if not os.path.exists("docs.md"):
        print("Warning: docs.md not found")
        return endpoints
    
    with open("docs.md", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Pattern to match endpoints like `GET endpoint/path` or `POST endpoint/path`
    pattern = r'`(GET|POST)\s+([^`]+)`'
    matches = re.findall(pattern, content)
    
    for method, path in matches:
        # Clean up the path
        path = path.strip()
        if path.startswith('/'):
            path = path[1:]
        
        if path not in endpoints:
            endpoints[path] = []
        endpoints[path].append(method)
    
    return endpoints

def get_implemented_endpoints() -> Dict[str, List[str]]:
    """Get all implemented endpoints from api/ directory"""
    endpoints = {}
    api_dir = Path("api")
    
    if not api_dir.exists():
        print("Warning: api/ directory not found")
        return endpoints
    
    # Walk through all Python files in api/
    for py_file in api_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue
        
        # Convert file path to URL path
        relative_path = py_file.relative_to(api_dir)
        url_parts = list(relative_path.parts[:-1])  # Remove .py extension
        url_parts.append(relative_path.stem)  # Add filename without extension
        
        url_path = "/".join(url_parts)
        
        # Check what methods are implemented
        methods = []
        try:
            with open(py_file, "r", encoding="utf-8") as f:
                content = f.read()
                
            if "@GET" in content and "def get(" in content:
                methods.append("GET")
            if "@POST" in content and "def post(" in content:
                methods.append("POST")
                
        except Exception as e:
            print(f"Error reading {py_file}: {e}")
            continue
        
        if methods:
            endpoints[url_path] = methods
    
    return endpoints

def normalize_path(path: str) -> str:
    """Normalize path for comparison"""
    # Remove leading/trailing slashes and convert to lowercase
    path = path.strip("/").lower()
    
    # Handle special cases
    replacements = {
        "auth/forgot_password": "auth/forgot_password",
        "auth/verify_recovery": "auth/verify_recovery",
        "account/update_info": "account/update_info",
        "account/bot_notifications/create": "account/bot_notifications/create",
        "account/bot_notifications/update": "account/bot_notifications/update",
        "account/bot_notifications/delete": "account/bot_notifications/delete",
        "service/rules/accept": "service/rules/accept",
    }
    
    return replacements.get(path, path)

def main():
    """Main validation function"""
    print("🔍 Checking route implementation...")
    print("=" * 50)
    
    # Get endpoints from documentation
    doc_endpoints = extract_endpoints_from_docs()
    print(f"📚 Found {len(doc_endpoints)} endpoints in docs.md")
    
    # Get implemented endpoints
    impl_endpoints = get_implemented_endpoints()
    print(f"💻 Found {len(impl_endpoints)} implemented endpoints")
    print()
    
    # Normalize paths for comparison
    normalized_doc = {normalize_path(k): v for k, v in doc_endpoints.items()}
    normalized_impl = {normalize_path(k): v for k, v in impl_endpoints.items()}
    
    # Check for missing implementations
    missing_endpoints = []
    method_mismatches = []
    
    for path, methods in normalized_doc.items():
        if path not in normalized_impl:
            missing_endpoints.append((path, methods))
        else:
            impl_methods = set(normalized_impl[path])
            doc_methods = set(methods)
            
            missing_methods = doc_methods - impl_methods
            if missing_methods:
                method_mismatches.append((path, list(missing_methods)))
    
    # Check for extra implementations
    extra_endpoints = []
    for path, methods in normalized_impl.items():
        if path not in normalized_doc:
            extra_endpoints.append((path, methods))
    
    # Report results
    if missing_endpoints:
        print("❌ Missing endpoint implementations:")
        for path, methods in missing_endpoints:
            print(f"   {path} ({', '.join(methods)})")
        print()
    
    if method_mismatches:
        print("⚠️  Method mismatches:")
        for path, methods in method_mismatches:
            print(f"   {path} missing: {', '.join(methods)}")
        print()
    
    if extra_endpoints:
        print("ℹ️  Extra implementations (not in docs):")
        for path, methods in extra_endpoints:
            print(f"   {path} ({', '.join(methods)})")
        print()
    
    # Summary
    total_issues = len(missing_endpoints) + len(method_mismatches)
    if total_issues == 0:
        print("✅ All documented endpoints are implemented!")
    else:
        print(f"📊 Summary: {total_issues} issues found")
        print(f"   - {len(missing_endpoints)} missing endpoints")
        print(f"   - {len(method_mismatches)} method mismatches")
    
    print(f"   - {len(extra_endpoints)} extra implementations")
    print()
    
    # Show some key endpoints that should be implemented
    key_endpoints = [
        "auth/login",
        "auth/register", 
        "account/info",
        "cards",
        "service/rules",
        "service/news"
    ]
    
    print("🔑 Key endpoints status:")
    for endpoint in key_endpoints:
        normalized = normalize_path(endpoint)
        if normalized in normalized_impl:
            methods = ", ".join(normalized_impl[normalized])
            print(f"   ✅ {endpoint} ({methods})")
        else:
            print(f"   ❌ {endpoint}")

if __name__ == "__main__":
    main()
