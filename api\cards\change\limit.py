"""
Cards change limit endpoint
/api/cards/change/limit
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class ChangeLimitRequest(BaseModel):
    card_id: int
    new_limit: float

@POST
def post(request):
    """Change card limit"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            change_request = ChangeLimitRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock change limit logic
        return jsonify({
            "status": "ok",
            "task_id": 17,
            "tasks": [
                {
                    "uid": "e8a98690-da79-49cd-ba1d-893ed8162797",
                    "card_id": change_request.card_id,
                    "action": "limit",
                    "new_limit": change_request.new_limit,
                    "status": "processing"
                }
            ]
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
