"""
Service news endpoint
/api/service/news
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock data
MOCK_NEWS = [
    {
        "id": 1,
        "header": "Обновление системы",
        "date": 1723423423,
        "is_read": False,
        "content": "Сегодня мы обновили нашу систему для улучшения производительности и безопасности. Все карты продолжают работать в штатном режиме.",
        "image": "https://example.com/news1.jpg"
    },
    {
        "id": 2,
        "header": "Новые возможности",
        "date": 1723323423,
        "is_read": True,
        "content": "Добавлены новые функции управления картами и улучшенная система уведомлений.",
        "image": None
    },
    {
        "id": 3,
        "header": "Техническое обслуживание",
        "date": 1723223423,
        "is_read": True,
        "content": "Запланировано техническое обслуживание на выходные. Возможны кратковременные перебои в работе.",
        "image": "https://example.com/news3.jpg"
    }
]

@GET
def get(request):
    """Get service news"""
    try:
        return jsonify({
            "status": "ok",
            "news": MOCK_NEWS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
