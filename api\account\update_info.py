"""
Account update info endpoint
/api/account/update_info
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional, Dict, Any
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST, GET

class NotificationsSettings(BaseModel):
    email: Optional[bool] = None
    push: Optional[bool] = None
    telegram: Optional[bool] = None

class TwoFASettings(BaseModel):
    enabled: Optional[bool] = None
    qr_code: Optional[str] = None

class UpdateInfoRequest(BaseModel):
    name: Optional[str] = None
    notifications: Optional[NotificationsSettings] = None
    two_fa: Optional[TwoFASettings] = None
    api_key: Optional[str] = None
    session_limit: Optional[str] = None
    card_visibility: Optional[bool] = None

# Mock current settings
MOCK_SETTINGS = {
    "name": "<PERSON> Markov",
    "notifications": {
        "email": True,
        "push": False,
        "telegram": True
    },
    "two_fa": {
        "enabled": True,
        "qr_code": "base64:iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    },
    "api_key": "api_key_12345",
    "session_limit": "single",
    "card_visibility": True
}

@POST
def post(request):
    """Update account info"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            update_request = UpdateInfoRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock update logic - in real implementation would update database
        return jsonify({
            "status": "ok"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500

@GET
def get(request):
    """Get current account info settings"""
    try:
        return jsonify({
            "status": "ok",
            **MOCK_SETTINGS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
