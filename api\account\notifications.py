"""
Account notifications endpoint
/api/account/notifications
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock notifications data
MOCK_NOTIFICATIONS = [
    {
        "id": 14,
        "datetime": **********,
        "priority": "high",
        "title": "Аккаунт заморожен",
        "content": "Ваш аккаунт заморожен."
    },
    {
        "id": 15,
        "datetime": **********,
        "priority": "normal",
        "title": "Новая карта выпущена",
        "content": "Карта **** 1234 успешно выпущена и готова к использованию."
    },
    {
        "id": 16,
        "datetime": **********,
        "priority": "low",
        "title": "Пополнение баланса",
        "content": "Баланс пополнен на $500.00"
    }
]

@GET
def get(request):
    """Get account notifications"""
    try:
        return jsonify({
            "status": "ok",
            "notifications": MOCK_NOTIFICATIONS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
