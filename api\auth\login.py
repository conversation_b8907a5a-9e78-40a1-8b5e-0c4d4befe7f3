"""
Auth login endpoint
/api/auth/login
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class LoginRequest(BaseModel):
    login: str
    password: str
    is_remember: bool = False

@POST
def post(request):
    """User login"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            login_request = LoginRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock authentication
        if login_request.login == "test" and login_request.password == "test":
            return jsonify({
                "status": "ok",
                "access_token": "mock_token_12345",
                "valid_to": 1723423423
            })
        else:
            return jsonify({
                "status": "error",
                "error": "invalid_credentials",
                "readable": "Неверные учетные данные"
            }), 401
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
