"""
Transactions transaction_id endpoint
/api/transactions/{transaction_id}
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock transaction data
MOCK_TRANSACTION = {
    "id": 1,
    "date": 1723423423,
    "is_decline": False,
    "card": {
        "id": 14,
        "number": "4111 **** **** 1234",
        "name": "Test Card"
    },
    "card_tags": ["production", "usa"],
    "card_name": "Test Card",
    "charge_amount": 100.50,
    "charge_currency": "USD",
    "requested_amount": 100.50,
    "requested_currency": "USD",
    "last_update": 1723423425,
    "last_status": "approved",
    "details": "Payment to Google Ads",
    "card_owner_id": 123
}

@GET
def get(request):
    """Get transaction information by ID"""
    try:
        # Get transaction_id from URL path
        transaction_id = request.view_args.get('transaction_id')
        
        if not transaction_id:
            return jsonify({
                "status": "error",
                "error": "missing_transaction_id",
                "readable": "Не указан ID транзакции"
            }), 400
        
        # Mock logic - return the same transaction with the requested ID
        transaction_data = MOCK_TRANSACTION.copy()
        transaction_data["id"] = transaction_id
        
        return jsonify({
            "status": "ok",
            "transaction": transaction_data
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
