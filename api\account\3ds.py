"""
Account 3DS endpoint
/api/account/3ds
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

class ThreeDSRequest(BaseModel):
    card_id: Optional[int] = None

# Mock 3DS records
MOCK_3DS_RECORDS = [
    {
        "id": 1,
        "card_id": 14,
        "date": **********,
        "content": "3DS authentication successful for transaction #12345"
    },
    {
        "id": 2,
        "card_id": 14,
        "date": **********,
        "content": "3DS challenge presented to cardholder"
    },
    {
        "id": 3,
        "card_id": 15,
        "date": **********,
        "content": "3DS authentication failed - timeout"
    },
    {
        "id": 4,
        "card_id": 16,
        "date": **********,
        "content": "3DS authentication successful for transaction #12346"
    },
    {
        "id": 5,
        "card_id": 14,
        "date": 1723423340,
        "content": "3DS frictionless authentication completed"
    }
]

@GET
def get(request):
    """Get 3DS records"""
    try:
        # Get query parameters
        card_id = request.args.get('card_id')
        
        # Filter records if card_id is provided
        filtered_records = MOCK_3DS_RECORDS
        if card_id:
            try:
                card_id = int(card_id)
                filtered_records = [r for r in MOCK_3DS_RECORDS if r["card_id"] == card_id]
            except ValueError:
                return jsonify({
                    "status": "error",
                    "error": "invalid_card_id",
                    "readable": "Неверный ID карты"
                }), 400
        
        return jsonify({
            "status": "ok",
            "3ds_records": filtered_records
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
