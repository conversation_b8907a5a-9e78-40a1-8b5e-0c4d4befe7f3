"""
Cards change unfreeze endpoint
/api/cards/change/unfreeze
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class UnfreezeRequest(BaseModel):
    card_id: int

@POST
def post(request):
    """Unfreeze card"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            unfreeze_request = UnfreezeRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock unfreeze logic
        return jsonify({
            "status": "ok",
            "task_id": 19,
            "tasks": [
                {
                    "uid": "e8a98690-da79-49cd-ba1d-893ed8162799",
                    "card_id": unfreeze_request.card_id,
                    "action": "unfreeze",
                    "status": "processing"
                }
            ]
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
