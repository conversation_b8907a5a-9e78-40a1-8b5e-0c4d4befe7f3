"""
Account info endpoint
/api/account/info
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock data
MOCK_ACCOUNT_INFO = {
    "name": "<PERSON>",
    "spend": 12412.32,
    "holds": 1234.32,
    "cards": 141,
    "decline_rate": 12.23,
    "account_level": "premium",
    "team": {
        "id": 15,
        "title": "TEST TEAM",
        "role": "buyer"
    },
    "manager": {
        "name": "<PERSON>",
        "social": "telegram",
        "social_username": "anton"
    },
    "charts": {
        "spend": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30],
        "by_transaction_type": {
            "Google": 151,
            "Facebook": 155,
            "Amazon": 89,
            "Microsoft": 67
        }
    },
    "banners": [
        {
            "timestamp": **********,
            "priority": "high",
            "title": "Добро пожаловать!",
            "content": "Ваш аккаунт успешно активирован",
            "is_dismissable": True
        }
    ]
}

@GET
def get(request):
    """Get account information"""
    try:
        return jsonify({
            "status": "ok",
            **MOCK_ACCOUNT_INFO
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
