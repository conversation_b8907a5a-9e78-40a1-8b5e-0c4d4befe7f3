"""
Tags update endpoint
/api/tags/update
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class UpdateTagRequest(BaseModel):
    tag_id: int
    name: Optional[str] = None
    color: Optional[str] = None

@POST
def post(request):
    """Update tag"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            update_request = UpdateTagRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock update logic
        return jsonify({
            "status": "ok"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
