"""
Tags create endpoint
/api/tags/create
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class CreateTagRequest(BaseModel):
    name: str
    color: str

@POST
def post(request):
    """Create new tag"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            create_request = CreateTagRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock create logic
        return jsonify({
            "status": "ok",
            "tag": {
                "id": 6,
                "name": create_request.name,
                "color": create_request.color,
                "cards_count": 0
            }
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
