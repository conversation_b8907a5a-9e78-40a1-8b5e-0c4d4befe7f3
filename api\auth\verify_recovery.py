"""
Auth verify recovery endpoint
/api/auth/verify_recovery
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class VerifyRecoveryRequest(BaseModel):
    recovery_code: str
    new_password: str
    new_password_reenter: str

@POST
def post(request):
    """Verify recovery and set new password"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            verify_request = VerifyRecoveryRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Check password match
        if verify_request.new_password != verify_request.new_password_reenter:
            return jsonify({
                "status": "error",
                "error": "password_mismatch",
                "readable": "Пароли не совпадают"
            }), 400
        
        # Mock verify recovery logic
        return jsonify({
            "status": "ok",
            "access_token": "recovery_token_12345",
            "valid_to": 1723423423
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
