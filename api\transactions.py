"""
Transactions endpoint
/api/transactions
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from run import POST

class TransactionsRequest(BaseModel):
    card_id: Optional[int] = None
    date_from: Optional[int] = None
    date_to: Optional[int] = None
    is_decline: Optional[bool] = None
    amount_from: Optional[float] = None
    amount_to: Optional[float] = None
    currency: Optional[str] = None
    tags: Optional[List[str]] = None
    order_by: Optional[str] = "date"
    order: Optional[str] = "desc"
    page: Optional[int] = 1
    per_page: Optional[int] = 50

# Mock transactions data
MOCK_TRANSACTIONS = [
    {
        "id": 1,
        "date": 1723423423,
        "is_decline": False,
        "card": {
            "id": 14,
            "number": "4111 **** **** 1234",
            "name": "Test Card"
        },
        "card_tags": ["production", "usa"],
        "card_name": "Test Card",
        "charge_amount": 100.50,
        "charge_currency": "USD",
        "requested_amount": 100.50,
        "requested_currency": "USD",
        "last_update": 1723423425,
        "last_status": "approved",
        "details": "Payment to Google Ads",
        "card_owner_id": 123
    },
    {
        "id": 2,
        "date": 1723323423,
        "is_decline": True,
        "card": {
            "id": 15,
            "number": "5555 **** **** 5678",
            "name": "Production Card"
        },
        "card_tags": ["production", "europe"],
        "card_name": "Production Card",
        "charge_amount": 250.00,
        "charge_currency": "EUR",
        "requested_amount": 250.00,
        "requested_currency": "EUR",
        "last_update": 1723323425,
        "last_status": "declined",
        "details": "Payment to Facebook Ads - Insufficient funds",
        "card_owner_id": 124
    },
    {
        "id": 3,
        "date": 1723223423,
        "is_decline": False,
        "card": {
            "id": 16,
            "number": "4444 **** **** 9999",
            "name": "Testing Card"
        },
        "card_tags": ["testing", "usa"],
        "card_name": "Testing Card",
        "charge_amount": 50.75,
        "charge_currency": "USD",
        "requested_amount": 50.75,
        "requested_currency": "USD",
        "last_update": 1723223425,
        "last_status": "approved",
        "details": "Payment to Amazon AWS",
        "card_owner_id": 125
    }
]

@POST
def post(request):
    """Get transactions list with filtering"""
    try:
        data = request.get_json() or {}
        
        try:
            transactions_request = TransactionsRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock filtering logic
        filtered_transactions = MOCK_TRANSACTIONS.copy()
        
        # Apply filters
        if transactions_request.card_id:
            filtered_transactions = [t for t in filtered_transactions if t["card"]["id"] == transactions_request.card_id]
        
        if transactions_request.is_decline is not None:
            filtered_transactions = [t for t in filtered_transactions if t["is_decline"] == transactions_request.is_decline]
        
        if transactions_request.currency:
            filtered_transactions = [t for t in filtered_transactions if t["charge_currency"] == transactions_request.currency]
        
        if transactions_request.tags:
            filtered_transactions = [t for t in filtered_transactions if any(tag in t["card_tags"] for tag in transactions_request.tags)]
        
        return jsonify({
            "status": "ok",
            "transaction_count": len(filtered_transactions),
            "per_page": transactions_request.per_page,
            "page": transactions_request.page,
            "max_page": 1,
            "transactions": filtered_transactions
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
