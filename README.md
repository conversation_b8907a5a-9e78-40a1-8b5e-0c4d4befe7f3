# Flask API with Pydantic

Простая реализация API на Flask с использованием Pydantic для валидации данных.

## Структура проекта

```
├── run.py                 # Основной файл запуска сервера
├── test_routes.py         # Скрипт проверки соответствия эндпоинтов документации
├── test.py               # Тесты API на работоспособность
├── requirements.txt      # Зависимости
├── docs.md              # Документация API
├── README.md            # Этот файл
└── api/                 # Папка с эндпоинтами
    ├── auth/
    │   └── login.py     # POST /api/auth/login
    ├── account/
    │   └── info.py      # GET /api/account/info
    ├── service/
    │   ├── rules.py     # GET/POST /api/service/rules
    │   └── news.py      # GET /api/service/news
    └── cards.py         # POST /api/cards
```

## Установка и запуск

### 1. Установка зависимостей

```bash
pip install -r requirements.txt
```

### 2. Запуск сервера

```bash
python run.py
```

Сервер запустится на `http://localhost:5000`

### 3. Проверка работоспособности

```bash
# Проверить соответствие эндпоинтов документации
python test_routes.py

# Запустить тесты API
python test.py
```

## Архитектура

### Декораторы HTTP методов

Каждый эндпоинт использует декораторы `@GET` и `@POST`:

```python
from run import GET, POST

@GET
def get(request):
    return jsonify({"status": "ok", "data": "..."})

@POST  
def post(request):
    return jsonify({"status": "ok"})
```

### Автоматическая регистрация эндпоинтов

Сервер автоматически сканирует папку `api/` и регистрирует все эндпоинты:

- `api/service/rules.py` → `/api/service/rules`
- `api/auth/login.py` → `/api/auth/login`
- `api/cards.py` → `/api/cards`

### Валидация с Pydantic

Используйте Pydantic модели для валидации входящих данных:

```python
from pydantic import BaseModel, ValidationError

class LoginRequest(BaseModel):
    login: str
    password: str
    is_remember: bool = False

@POST
def post(request):
    data = request.get_json()
    try:
        login_request = LoginRequest(**data)
        # Обработка валидных данных
    except ValidationError as e:
        return jsonify({
            "status": "error",
            "error": "validation_error",
            "details": e.errors()
        }), 400
```

## Примеры использования

### Получение правил сервиса

```bash
curl -X GET http://localhost:5000/api/service/rules
```

### Авторизация

```bash
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"login": "test", "password": "test", "is_remember": true}'
```

### Получение информации об аккаунте

```bash
curl -X GET http://localhost:5000/api/account/info
```

## Добавление новых эндпоинтов

1. Создайте файл в папке `api/` по нужному пути
2. Импортируйте декораторы из `run.py`
3. Создайте функции с декораторами `@GET` или `@POST`
4. Перезапустите сервер

Пример нового эндпоинта `api/users/profile.py`:

```python
from flask import jsonify
from run import GET, POST

@GET
def get(request):
    return jsonify({
        "status": "ok",
        "profile": {"name": "User", "email": "<EMAIL>"}
    })

@POST
def post(request):
    # Обновление профиля
    return jsonify({"status": "ok"})
```

Эндпоинт будет доступен по адресу `/api/users/profile`

## Особенности

- **Mock данные**: Все эндпоинты возвращают примерные данные
- **Глобальная переменная database**: Доступна во всех модулях как `database = None`
- **Автоматическая обработка ошибок**: Все исключения автоматически обрабатываются
- **Валидация JSON**: Поддержка Pydantic для валидации входящих данных
- **Проверка соответствия**: Скрипт `test_routes.py` проверяет соответствие реализации документации

## Тестирование

Запустите `python test.py` для проверки основных эндпоинтов на статус 200.

Запустите `python test_routes.py` для проверки соответствия реализованных эндпоинтов документации.
