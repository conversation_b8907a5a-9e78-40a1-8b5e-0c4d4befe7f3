"""
Cards change info endpoint
/api/cards/change/info
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import List, Optional
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class ChangeInfoRequest(BaseModel):
    card_id: int
    title: Optional[str] = None
    notes: Optional[str] = None
    tags: Optional[List[str]] = None

@POST
def post(request):
    """Change card info"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            change_request = ChangeInfoRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock change info logic
        return jsonify({
            "status": "ok",
            "task_id": 16,
            "tasks": [
                {
                    "uid": "e8a98690-da79-49cd-ba1d-893ed8162796",
                    "card_id": change_request.card_id,
                    "action": "info",
                    "status": "processing"
                }
            ]
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
