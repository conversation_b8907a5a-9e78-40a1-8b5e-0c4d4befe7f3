"""
Cards 3DS endpoint
/api/cards/{card_id}/3ds
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import GET

# Mock 3DS records for specific card
MOCK_CARD_3DS_RECORDS = [
    {
        "id": 1,
        "card_id": 14,
        "date": 1723423423,
        "content": "3DS authentication successful for transaction #12345"
    },
    {
        "id": 2,
        "card_id": 14,
        "date": 1723423400,
        "content": "3DS challenge presented to cardholder"
    },
    {
        "id": 5,
        "card_id": 14,
        "date": 1723423340,
        "content": "3DS frictionless authentication completed"
    }
]

@GET
def get(request):
    """Get 3DS records for specific card"""
    try:
        # Get card_id from URL path
        card_id = request.view_args.get('card_id')
        
        if not card_id:
            return jsonify({
                "status": "error",
                "error": "missing_card_id",
                "readable": "Не указан ID карты"
            }), 400
        
        # Mock logic - filter records by card_id
        filtered_records = [
            {**record, "card_id": card_id} 
            for record in MOCK_CARD_3DS_RECORDS
        ]
        
        return jsonify({
            "status": "ok",
            "3ds_records": filtered_records
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
