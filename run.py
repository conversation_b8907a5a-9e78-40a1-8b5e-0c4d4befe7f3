#!/usr/bin/env python3
"""
Main Flask application entry point
"""

import os
import sys
import importlib
from pathlib import Path
from flask import Flask, request, jsonify
from functools import wraps
import traceback

# Global database variable (mock)
database = None

# Flask app instance
app = Flask(__name__)

# Store registered endpoints for validation
registered_endpoints = {}

def GET(func):
    """Decorator for GET endpoints"""
    func._http_method = 'GET'
    func._endpoint_func = func
    return func

def POST(func):
    """Decorator for POST endpoints"""
    func._http_method = 'POST'
    func._endpoint_func = func
    return func

def register_endpoint(module_path, url_path):
    """Register an endpoint from a module"""
    try:
        # Import the module
        module_name = module_path.replace('/', '.').replace('.py', '')
        if module_name.startswith('.'):
            module_name = module_name[1:]
        
        spec = importlib.util.spec_from_file_location(module_name, module_path)
        module = importlib.util.module_from_spec(spec)
        
        # Add global database to module
        module.database = database
        
        spec.loader.exec_module(module)
        
        # Find decorated functions
        for attr_name in dir(module):
            attr = getattr(module, attr_name)
            if callable(attr) and hasattr(attr, '_http_method'):
                method = attr._http_method
                endpoint_name = f"{url_path}_{method.lower()}"

                # Create Flask route with proper closure
                def make_route_handler(func, endpoint):
                    @wraps(func)
                    def route_handler(*args, **kwargs):
                        try:
                            # Call the actual function with request object
                            return func(request)
                        except Exception as e:
                            print(f"Error in {endpoint}: {str(e)}")
                            traceback.print_exc()
                            return jsonify({
                                "status": "error",
                                "error": "system_error",
                                "readable": "Системная ошибка"
                            }), 500
                    return route_handler

                app.add_url_rule(url_path, endpoint=endpoint_name,
                               view_func=make_route_handler(attr, endpoint_name),
                               methods=[method])

                registered_endpoints[url_path] = {
                    'method': method,
                    'module': module_path,
                    'function': attr_name
                }

                print(f"Registered: {method} {url_path} -> {module_path}:{attr_name}")
                
    except Exception as e:
        print(f"Failed to register {module_path}: {str(e)}")
        traceback.print_exc()

def discover_and_register_endpoints():
    """Discover and register all endpoints from api/ directory"""
    api_dir = Path("api")

    if not api_dir.exists():
        print("Warning: api/ directory not found")
        return

    # Walk through all Python files in api/
    for py_file in api_dir.rglob("*.py"):
        if py_file.name == "__init__.py":
            continue

        # Convert file path to URL path
        relative_path = py_file.relative_to(api_dir)
        url_parts = list(relative_path.parts[:-1])  # Remove .py extension
        url_parts.append(relative_path.stem)  # Add filename without extension

        url_path = "/api/" + "/".join(url_parts)

        # Handle dynamic routes like cards/{card_id}
        if relative_path.stem == "card_id":
            url_path = url_path.replace("/card_id", "/<int:card_id>")
        elif relative_path.stem == "transaction_id":
            url_path = url_path.replace("/transaction_id", "/<int:transaction_id>")

        register_endpoint(str(py_file), url_path)

@app.errorhandler(404)
def not_found(error):
    return jsonify({
        "status": "error",
        "error": "not_found",
        "readable": "Эндпоинт не найден"
    }), 404

@app.errorhandler(405)
def method_not_allowed(error):
    return jsonify({
        "status": "error",
        "error": "method_not_allowed",
        "readable": "Метод не разрешен"
    }), 405

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "ok",
        "message": "API is running",
        "endpoints": len(registered_endpoints)
    })

if __name__ == "__main__":
    print("Starting Flask API server...")
    print("Database:", database)
    
    # Discover and register all endpoints
    discover_and_register_endpoints()
    
    print(f"Registered {len(registered_endpoints)} endpoints")
    
    # Run the app
    app.run(debug=True, host="0.0.0.0", port=5000)
