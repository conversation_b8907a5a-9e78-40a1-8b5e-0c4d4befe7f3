"""
Account actions endpoint
/api/account/actions
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock actions data
MOCK_ACTIONS = [
    {
        "id": 14,
        "datetime": **********,
        "action_on": "cards",
        "action": "freeze",
        "cards": [
            {
                "id": 14,
                "title": "Test Card",
                "card_number": "4111 **** **** 1234"
            }
        ],
        "notes": "Заморожено по запросу пользователя"
    },
    {
        "id": 15,
        "datetime": **********,
        "action_on": "account",
        "action": "limit_update",
        "cards": [],
        "notes": "Обновлен лимит аккаунта"
    },
    {
        "id": 16,
        "datetime": **********,
        "action_on": "balance",
        "action": "topup",
        "cards": [],
        "notes": "Пополнение баланса на $500"
    }
]

@GET
def get(request):
    """Get account actions"""
    try:
        return jsonify({
            "status": "ok",
            "actions": MOCK_ACTIONS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
