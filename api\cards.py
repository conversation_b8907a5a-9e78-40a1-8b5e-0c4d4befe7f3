"""
Cards list endpoint
/api/cards
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from run import POST

class CardsRequest(BaseModel):
    title: Optional[str] = None
    lasts: Optional[str] = None
    starts: Optional[str] = None
    balance_more: Optional[float] = None
    balance_less: Optional[float] = None
    card_type: Optional[str] = None
    card_scheme: Optional[str] = None
    tags: Optional[List[str]] = None
    order_by: Optional[str] = "order_time"
    order: Optional[str] = "desc"
    excel: Optional[bool] = False
    page: Optional[int] = 1
    per_page: Optional[int] = 100

# Mock data
MOCK_CARDS = [
    {
        "id": 14,
        "title": "Test card",
        "notes": "Основная карта для тестов",
        "tags": ["test_tags", "test2"],
        "number": "****************",
        "exp_date": "03/25",
        "cvv": "123",
        "ordered_time": 1723423423,
        "issued_time": 1723423423,
        "last_transaction": 1745645645,
        "card_type": "balanced",
        "balance_id": -1,
        "balance": 500,
        "limit": 500,
        "scheme": "visa",
        "can_topup": True,
        "spend": 123.42,
        "hold": 0.00,
        "decline_rate": 0.00,
        "daily_spend": 50.00,
        "total_spend": 123.42,
        "daily_hold": 10.00,
        "total_hold": 0.00,
        "status": "active"
    },
    {
        "id": 15,
        "title": "Production card",
        "notes": "Карта для продакшена",
        "tags": ["production", "usa"],
        "number": "****************",
        "exp_date": "05/26",
        "cvv": "456",
        "ordered_time": 1723323423,
        "issued_time": 1723323423,
        "last_transaction": 1745545645,
        "card_type": "linked",
        "balance_id": 1,
        "balance": 1000,
        "limit": 1000,
        "scheme": "mastercard",
        "can_topup": False,
        "spend": 456.78,
        "hold": 50.00,
        "decline_rate": 2.5,
        "daily_spend": 100.00,
        "total_spend": 456.78,
        "daily_hold": 50.00,
        "total_hold": 50.00,
        "status": "active"
    }
]

@POST
def post(request):
    """Get cards list with filtering"""
    try:
        data = request.get_json() or {}
        
        try:
            cards_request = CardsRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock filtering (in real implementation would filter from database)
        filtered_cards = MOCK_CARDS.copy()
        
        # Apply filters
        if cards_request.title:
            filtered_cards = [c for c in filtered_cards if cards_request.title.lower() in c["title"].lower()]
        
        if cards_request.tags:
            filtered_cards = [c for c in filtered_cards if any(tag in c["tags"] for tag in cards_request.tags)]
        
        if cards_request.card_scheme:
            filtered_cards = [c for c in filtered_cards if c["scheme"] == cards_request.card_scheme]
        
        return jsonify({
            "status": "ok",
            "card_count": len(filtered_cards),
            "per_page": cards_request.per_page,
            "page": cards_request.page,
            "max_page": 1,
            "cards": filtered_cards
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
