"""
Account bot notifications create endpoint
/api/account/bot_notifications/create
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class CreateChatRequest(BaseModel):
    chat_id: str
    chat_name: str
    is_enabled: bool = True

@POST
def post(request):
    """Create bot notification chat"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            create_request = CreateChatRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock create logic
        return jsonify({
            "status": "ok",
            "chat": {
                "id": 4,
                "chat_id": create_request.chat_id,
                "chat_name": create_request.chat_name,
                "is_enabled": create_request.is_enabled,
                "created_at": 1723423423
            }
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
