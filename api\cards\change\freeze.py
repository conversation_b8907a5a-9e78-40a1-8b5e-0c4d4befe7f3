"""
Cards change freeze endpoint
/api/cards/change/freeze
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class FreezeRequest(BaseModel):
    card_id: int

@POST
def post(request):
    """Freeze card"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            freeze_request = FreezeRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock freeze logic
        return jsonify({
            "status": "ok",
            "task_id": 18,
            "tasks": [
                {
                    "uid": "e8a98690-da79-49cd-ba1d-893ed8162798",
                    "card_id": freeze_request.card_id,
                    "action": "freeze",
                    "status": "processing"
                }
            ]
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
