"""
Auth retry endpoint
/api/auth/retry
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class RetryRequest(BaseModel):
    retry_code: str
    email: str

@POST
def post(request):
    """Retry confirmation code"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            retry_request = RetryRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock retry logic
        return jsonify({
            "status": "ok",
            "email": retry_request.email,
            "sent_email": True,
            "retry_code": "RETRY789012",
            "next_retry": 1723432423
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
