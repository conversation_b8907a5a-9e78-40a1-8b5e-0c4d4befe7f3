"""
Account balances endpoint
/api/account/balances
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock balances data
MOCK_BALANCES = [
    {
        "id": 14,
        "title": "USD Balance",
        "type": "system",
        "balance": 124.00,
        "hold": 0.00,
        "available": 124.00,
        "currency": "USD"
    },
    {
        "id": 15,
        "title": "EUR Balance",
        "type": "system",
        "balance": 89.50,
        "hold": 10.00,
        "available": 79.50,
        "currency": "EUR"
    },
    {
        "id": 16,
        "title": "Personal Wallet",
        "type": "personal",
        "balance": 1500.00,
        "hold": 50.00,
        "available": 1450.00,
        "currency": "USD"
    }
]

@GET
def get(request):
    """Get account balances"""
    try:
        return jsonify({
            "status": "ok",
            "balances": MOCK_BALANCES
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
