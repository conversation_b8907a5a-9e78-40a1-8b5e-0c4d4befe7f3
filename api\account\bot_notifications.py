"""
Account bot notifications endpoint
/api/account/bot_notifications
"""

from flask import jsonify
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET

# Mock bot notifications data
MOCK_CHATS = [
    {
        "id": 1,
        "chat_id": "*********",
        "chat_name": "Main Chat",
        "is_enabled": True,
        "created_at": **********
    },
    {
        "id": 2,
        "chat_id": "*********",
        "chat_name": "Backup Chat",
        "is_enabled": False,
        "created_at": **********
    },
    {
        "id": 3,
        "chat_id": "*********",
        "chat_name": "<PERSON><PERSON> Chat",
        "is_enabled": True,
        "created_at": **********
    }
]

@GET
def get(request):
    """Get bot notification chats"""
    try:
        return jsonify({
            "status": "ok",
            "chats": MOCK_CHATS
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
