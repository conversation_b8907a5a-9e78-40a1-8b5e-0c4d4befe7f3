"""
Account withdraw endpoint
/api/account/withdraw
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class WithdrawRequest(BaseModel):
    type: str
    args: List[str]
    amount: float
    from_balance_id: int

@POST
def post(request):
    """Account withdraw"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            withdraw_request = WithdrawRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock withdraw logic
        return jsonify({
            "status": "ok",
            "transaction_id": "WTH123456",
            "amount": withdraw_request.amount,
            "type": withdraw_request.type,
            "from_balance_id": withdraw_request.from_balance_id,
            "status": "processing"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
