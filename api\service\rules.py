"""
Service rules endpoint
/api/service/rules
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import Optional
import json

# Import decorators from run.py
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import GET, POST

# Pydantic models
class RulesAcceptRequest(BaseModel):
    version: str

# Mock data
MOCK_RULES = {
    "content": "Правила использования сервиса:\n\n1. Пользователь обязуется использовать сервис только в законных целях.\n2. Запрещается передача данных карт третьим лицам.\n3. Пользователь несет ответственность за безопасность своего аккаунта.\n4. Сервис оставляет за собой право заблокировать аккаунт при нарушении правил.\n5. Все операции логируются и могут быть переданы в правоохранительные органы.",
    "is_accepted": True,
    "last_updated": 1723423423,
    "version": "1.2"
}

@GET
def get(request):
    """Get service rules"""
    try:
        return jsonify({
            "status": "ok",
            "rules": MOCK_RULES
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500

@POST
def post(request):
    """Accept service rules"""
    try:
        # Get JSON data
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        # Validate with Pydantic
        try:
            rules_request = RulesAcceptRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock acceptance logic
        if rules_request.version != MOCK_RULES["version"]:
            return jsonify({
                "status": "error",
                "error": "invalid_version",
                "readable": "Неверная версия правил"
            }), 400
        
        # In real implementation, would update database
        # database.update_user_rules_acceptance(user_id, rules_request.version)
        
        return jsonify({
            "status": "ok"
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
