"""
Account topup endpoint
/api/account/topup
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
from typing import List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class TopupRequest(BaseModel):
    type: str
    args: List[str]
    amount: float
    to_balance_id: int

@POST
def post(request):
    """Account topup"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            topup_request = TopupRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock topup logic
        return jsonify({
            "status": "ok",
            "transaction_id": "TXN123456",
            "amount": topup_request.amount,
            "type": topup_request.type,
            "to_balance_id": topup_request.to_balance_id,
            "status": "processing"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
