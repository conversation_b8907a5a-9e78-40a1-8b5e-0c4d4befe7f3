"""
Service rules accept endpoint
/api/service/rules/accept
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))
from run import POST

class AcceptRulesRequest(BaseModel):
    version: str

@POST
def post(request):
    """Accept service rules"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            accept_request = AcceptRulesRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock accept logic
        if accept_request.version != "1.2":
            return jsonify({
                "status": "error",
                "error": "invalid_version",
                "readable": "Неверная версия правил"
            }), 400
        
        return jsonify({
            "status": "ok"
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
