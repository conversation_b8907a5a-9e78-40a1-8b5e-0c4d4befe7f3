"""
Auth register endpoint
/api/auth/register
"""

from flask import jsonify
from pydantic import BaseModel, ValidationError
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from run import POST

class RegisterRequest(BaseModel):
    name: str
    email: str
    password: str
    social: str
    social_username: str
    team_code: str

@POST
def post(request):
    """User registration"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "error": "invalid_data",
                "readable": "Неверные данные"
            }), 400
        
        try:
            register_request = RegisterRequest(**data)
        except ValidationError as e:
            return jsonify({
                "status": "error",
                "error": "validation_error",
                "readable": "Ошибка валидации данных",
                "details": e.errors()
            }), 400
        
        # Mock registration logic
        return jsonify({
            "status": "ok",
            "sent_email": True,
            "retry_code": "RETRY123456",
            "next_retry": 1723432423
        })
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": "system_error",
            "readable": "Системная ошибка"
        }), 500
